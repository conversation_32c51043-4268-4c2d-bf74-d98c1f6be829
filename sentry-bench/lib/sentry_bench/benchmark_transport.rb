# frozen_string_literal: true

begin
  require 'sentry-ruby'
rescue LoadError
  # Try to load from the relative path in the repository
  $LOAD_PATH.unshift File.expand_path('../../../sentry-ruby/lib', __dir__)
  require 'sentry-ruby'
end

module SentryBench
  # A transport class designed for benchmarking that captures events without sending them
  # This allows for memory profiling and performance testing without network overhead
  class BenchmarkTransport < Sentry::Transport
    attr_accessor :events, :envelopes, :log_events

    def initialize(*)
      super
      @events = []
      @envelopes = []
      @log_events = []
    end

    def send_event(event)
      @events << event
      event
    end

    def send_envelope(envelope)
      @envelopes << envelope

      # Track log events separately for analysis
      envelope.items.each do |item|
        if item.type == "log"
          @log_events << item.payload
        end
      end

      envelope
    end

    def send_data(data, options = {})
      # No-op for benchmarking - we don't actually send data
      true
    end

    # Helper methods for benchmark analysis
    def total_events
      @events.size
    end

    def total_envelopes
      @envelopes.size
    end

    def total_log_events
      @log_events.size
    end

    def clear!
      @events.clear
      @envelopes.clear
      @log_events.clear
    end

    def memory_usage_summary
      {
        events: @events.size,
        envelopes: @envelopes.size,
        log_events: @log_events.size,
        events_memory: calculate_object_memory(@events),
        envelopes_memory: calculate_object_memory(@envelopes),
        log_events_memory: calculate_object_memory(@log_events)
      }
    end

    private

    def calculate_object_memory(objects)
      # Simple estimation - in a real scenario you'd use more sophisticated memory calculation
      objects.sum { |obj| obj.to_s.bytesize }
    end
  end
end
